🔬 RAPPORT COMPLET ET DÉTAILLÉ - ANALYSE LIGNE PAR LIGNE
📐 INTERPRÉTATION EXACTE DE CHAQUE MÉTRIQUE
🎯 CALCULS PRÉCIS DANS LE PROGRAMME
1. MÉTRIQUE : block_entropies[end] / max_length

Signification : Approximation de l'entropie métrique de Kolmogorov-Sinai
Interprétation : Mesure la complexité dynamique du système. Valeur élevée = chaos, valeur faible = ordre
2. CONDITIONNELLE : H(Xₙ|Xₙ₋₁) - Entropie conditionnelle avec contexte de longueur 1

Calcul : Compte les transitions contexte→symbole suivant, puis calcule l'entropie moyenne
Interprétation : Incertitude sur le prochain symbole connaissant le symbole actuel. Élevée = imprévisible
3. T4 : block_entropies[end] - Entropie du bloc de longueur maximale (4)

Calcul : Entropie moyenne des sous-séquences de longueur 4
Interprétation : Information moyenne par symbole dans des blocs de 4. Mesure la complexité locale
4. DivEntropG : H(X) = -∑ p_obs(x) log₂ p_obs(x) - Shannon avec fréquences observées

Calcul : Compte les fréquences réelles, calcule l'entropie de Shannon
Interprétation : Diversité réelle des résultats observés. Élevée = grande variété
5. EntropG : H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ)) - AEP avec probabilités théoriques

Calcul : Utilise les probabilités théoriques INDEX5 pour calculer l'information
Interprétation : Information théorique moyenne par symbole selon le modèle
6-9. DIFFÉRENTIELS : |Métrique(n) - Métrique(n-1)|

Interprétation : Mesure les changements brusques dans chaque métrique
10. SCORE : (DiffC + DiffEG) / (DiffT4 + DiffDivEG)

Interprétation : Ratio des changements "prédictifs" sur les changements "structurels"