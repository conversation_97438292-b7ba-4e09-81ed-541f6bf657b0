FORMULES MATHÉMATIQUES DES MÉTRIQUES D'ENTROPIE BACCARAT
=========================================================

Ce fichier contient toutes les formules mathématiques utilisées dans entropie_baccarat_analyzer.py
pour calculer les métriques d'entropie INDEX5 du baccarat, organisées par catégories.

═══════════════════════════════════════════════════════════════════════════════
COLLECTE DES DONNÉES DEPUIS LE FICHIER JSON
═══════════════════════════════════════════════════════════════════════════════

📁 FICHIER SOURCE :
Le programme utilise le fichier JSON suivant comme source de données :
"partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"

🔧 MÉTHODE DE CHARGEMENT :
```python
def load_baccarat_data(self, filepath: str) -> List[Dict]:
    """
    📁 CHARGEMENT - Charge les données de baccarat depuis le fichier JSON

    Gère différentes structures JSON possibles et valide les données chargées.
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Vérifier la structure du JSON
        if isinstance(data, dict) and 'parties_condensees' in data:
            # Structure: {"parties_condensees": [...]}
            parties = data['parties_condensees']
            print(f"✅ Données chargées: {len(parties)} parties trouvées")
            return parties
        elif isinstance(data, list):
            # Structure: [partie1, partie2, ...]
            print(f"✅ Données chargées: {len(data)} parties trouvées")
            return data
        else:
            print(f"❌ Structure JSON non reconnue. Clés disponibles: {list(data.keys())}")
            return []

    except FileNotFoundError:
        print(f"❌ Erreur: Fichier {filepath} non trouvé")
        return []
    except json.JSONDecodeError as e:
        print(f"❌ Erreur JSON: {e}")
        return []
```

📊 STRUCTURES JSON SUPPORTÉES :

STRUCTURE 1 - Avec clé "parties_condensees" :
```json
{
    "parties_condensees": [
        {
            "hands": [...] ou "mains_condensees": [...]
        }
    ]
}
```

STRUCTURE 2 - Liste directe :
```json
[
    {
        "hands": [...] ou "mains_condensees": [...]
    }
]
```

🔍 EXTRACTION DES SÉQUENCES INDEX5 :
```python
def extract_index5_sequence(self, game_data: Dict) -> List[str]:
    """
    🔍 EXTRACTION - Extrait la séquence INDEX5 d'une partie

    Filtre les mains d'ajustement et extrait uniquement les valeurs INDEX5 valides
    pour l'analyse entropique.
    """
    sequence = []

    # STRUCTURE A - Clé "hands"
    if 'hands' in game_data:
        for hand in game_data['hands']:
            # Exclure les mains d'ajustement (main_number null ou INDEX5 vide)
            if (hand.get('main_number') is not None and
                'INDEX5' in hand and
                hand['INDEX5'] and
                hand['INDEX5'].strip()):
                sequence.append(hand['INDEX5'])

    # STRUCTURE B - Clé "mains_condensees"
    elif 'mains_condensees' in game_data:
        for main in game_data['mains_condensees']:
            # Exclure les mains d'ajustement (main_number null ou index5 vide)
            if (main.get('main_number') is not None and
                'index5' in main and
                main['index5'] and
                main['index5'].strip()):
                sequence.append(main['index5'])

    print(f"🔍 Séquence extraite: {len(sequence)} mains valides (mains d'ajustement exclues)")
    return sequence
```

📋 CRITÈRES DE FILTRAGE :
• main_number ≠ null (exclut les mains d'ajustement)
• INDEX5/index5 non vide et non null
• INDEX5/index5 après suppression des espaces non vide
• Seules les mains valides sont conservées pour l'analyse

🎯 FORMATS INDEX5 ATTENDUS :
Les valeurs INDEX5 extraites doivent respecter le format :
"INDEX1_INDEX2_INDEX3" où :
• INDEX1 ∈ {0, 1}
• INDEX2 ∈ {A, B, C}
• INDEX3 ∈ {BANKER, PLAYER, TIE}

Exemples : "0_A_BANKER", "1_C_TIE", "0_B_PLAYER"

💾 GESTION DES ERREURS :
• FileNotFoundError : Fichier JSON introuvable
• JSONDecodeError : Format JSON invalide
• Structure non reconnue : Clés JSON inattendues
• Données vides : Aucune séquence INDEX5 valide trouvée

TABLE DES MATIÈRES :
1. MÉTRIQUE (Entropie Métrique - Kolmogorov-Sinai)
2. CONDITIONNELLE (Entropie Conditionnelle)
3. TAUX (Taux d'Entropie - Entropy Rate)
4. ENTROPG (Entropie Générale - AEP)
5. DIVENTROPG (Diversité Entropique - Shannon)
6. DIFFCOND / DIFFC (Différentiel Entropie Conditionnelle)
7. DIFFTAUX / DIFFT (Différentiel Taux d'Entropie)
8. DIFFDIVRENTROPG / DIVEG (Différentiel Diversité Entropique)
9. DIFFENTROPG / ENTG (Différentiel Entropie Générale)
10. SCORE PRÉDICTIF COMPOSITE
11. H_AEP (Formule Unifiée)
12. RÈGLES INDEX1 (Transitions Déterministes)

═══════════════════════════════════════════════════════════════════════════════
1. MÉTRIQUE (Entropie Métrique - Kolmogorov-Sinai)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
L'entropie métrique h_μ(T) mesure le taux de création d'information par symbole
dans un système dynamique selon la théorie de Kolmogorov-Sinai.

📐 FORMULE THÉORIQUE :
h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

💻 IMPLÉMENTATION PRATIQUE :
h_metric = H(max_length) / max_length

🔧 ALGORITHME DÉTAILLÉ :
```python
def _estimate_metric_entropy(sequence, max_length):
    # Étape 1: Calculer entropies de blocs brutes
    block_entropies_raw = _calculate_block_entropies_raw(sequence, max_length)

    # Étape 2: Calculer h_n = H(n)/n pour chaque longueur
    h_metric_estimates = []
    for i, entropy in enumerate(block_entropies_raw):
        block_length = i + 1
        h_estimate = entropy / block_length  # H(n)/n
        h_metric_estimates.append(h_estimate)

    # Étape 3: Approximation de la limite
    h_metric = h_metric_estimates[-1]  # Dernière estimation
    return max(0.0, h_metric)
```

📊 CALCUL DES ENTROPIES DE BLOCS :
Pour chaque longueur de bloc k :
1. Créer toutes les sous-séquences de longueur k
2. Calculer l'entropie AEP moyenne de ces sous-séquences
3. H(k) = (1/N) × ∑ᵢ₌₁ᴺ H_AEP(sous-séquence_i)

🎲 INTERPRÉTATION :
• Valeur élevée → Système chaotique, imprévisible
• Valeur faible → Système ordonné, patterns détectables
• Unité : bits par symbole

═══════════════════════════════════════════════════════════════════════════════
2. CONDITIONNELLE (Entropie Conditionnelle)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
L'entropie conditionnelle H(X|Y) mesure l'incertitude moyenne de X sachant Y.
Dans notre cas : H(Xₙ|Xₙ₋₁) - incertitude du symbole suivant sachant le précédent.

📐 FORMULE THÉORIQUE :
H(X|Y) = ∑ P(y) × H(X|Y=y) = -∑∑ P(x,y) × log₂ P(x|y)

💻 IMPLÉMENTATION PRATIQUE :
Contexte de longueur 1 : H(Xₙ|Xₙ₋₁)

🔧 ALGORITHME DÉTAILLÉ :
```python
def _calculate_conditional_entropy(sequence):
    context_length = 1  # Contexte fixe de longueur 1
    context_transitions = {}

    # Étape 1: Compter les transitions contexte → symbole suivant
    for i in range(len(sequence) - context_length):
        context = sequence[i]  # Symbole précédent
        next_symbol = sequence[i + context_length]  # Symbole suivant

        if context not in context_transitions:
            context_transitions[context] = {}
        context_transitions[context][next_symbol] += 1

    # Étape 2: Calculer H(X|Contexte) = ∑ P(contexte) × H(X|contexte)
    total_transitions = sum(sum(transitions.values()) for transitions in context_transitions.values())
    conditional_entropy = 0.0

    for context, transitions in context_transitions.items():
        context_prob = sum(transitions.values()) / total_transitions

        # Créer séquence des symboles suivants pour ce contexte
        context_sequence = []
        for next_symbol, count in transitions.items():
            context_sequence.extend([next_symbol] * count)

        # H(X|ce contexte) calculé selon AEP
        context_entropy = _calculate_sequence_entropy_aep(context_sequence)
        conditional_entropy += context_prob * context_entropy

    return conditional_entropy
```

🎲 INTERPRÉTATION :
• Valeur élevée → Faible prédictibilité contextuelle
• Valeur faible → Forte prédictibilité basée sur le contexte précédent
• Unité : bits

═══════════════════════════════════════════════════════════════════════════════
3. TAUX (Taux d'Entropie - Entropy Rate)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
Le taux d'entropie représente la limite asymptotique de l'information moyenne
générée par symbole dans une séquence.

📐 FORMULE THÉORIQUE :
H_rate = lim_{n→∞} (1/n) H(X₁, X₂, ..., Xₙ)

💻 IMPLÉMENTATION PRATIQUE :
entropy_rate = block_entropies[-1]  # Entropie du dernier bloc

🔧 ALGORITHME DÉTAILLÉ :
```python
def _calculate_block_entropies(sequence, max_length):
    entropies = []

    for block_len in range(1, min(max_length + 1, len(sequence) + 1)):
        if block_len == 1:
            # Pour longueur 1 : entropie AEP de la séquence complète
            block_entropy = _calculate_sequence_entropy_aep(sequence)
        else:
            # Pour longueur > 1 : entropie moyenne des sous-séquences
            block_sequences = []
            for i in range(len(sequence) - block_len + 1):
                block_sequence = sequence[i:i+block_len]
                block_sequences.append(block_sequence)

            total_entropy = 0.0
            for block_seq in block_sequences:
                total_entropy += _calculate_sequence_entropy_aep(block_seq)

            block_entropy = total_entropy / len(block_sequences)

        entropies.append(block_entropy)

    return entropies

# Usage dans calculate_block_entropy_evolution()
'entropy_rate': block_entropies[-1] if block_entropies else 0
```

🎲 INTERPRÉTATION :
• Valeur élevée → Information dense, peu de redondance
• Valeur faible → Information compressible, patterns répétitifs
• Unité : bits par symbole

═══════════════════════════════════════════════════════════════════════════════
4. ENTROPG (Entropie Générale - Entropie Théorique AEP)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
L'entropie générale utilise les probabilités théoriques INDEX5 selon la formule
AEP (Asymptotic Equipartition Property).

📐 FORMULE THÉORIQUE AEP :
H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))

💾 PROBABILITÉS THÉORIQUES INDEX5 :
```python
theoretical_probs = {
    '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389,
    '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479,
    '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929,
    '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361,
    '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888,
    '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352,
    '0_A_TIE': 0.017719, '1_A_TIE': 0.017978,
    '0_B_TIE': 0.016281, '1_B_TIE': 0.016482,
    '0_C_TIE': 0.013241, '1_C_TIE': 0.013423
}
# Normalisées pour que la somme = 1.0
```

🔧 ALGORITHME DÉTAILLÉ :
```python
def _calculate_sequence_entropy_aep(sequence):
    if not sequence:
        return 0.0

    # Calculer log₂ de la probabilité jointe théorique
    total_log_prob = 0.0
    for value in sequence:
        if value in theoretical_probs:
            p_theo = theoretical_probs[value]
            if p_theo > 0:
                total_log_prob += math.log2(p_theo)
            else:
                total_log_prob += math.log2(epsilon)  # epsilon = 1e-12
        else:
            total_log_prob += math.log2(epsilon)  # Valeur non trouvée

    # Retourner l'entropie AEP : -(1/n) × ∑log₂(p_théo(xᵢ))
    return -total_log_prob / len(sequence)
```

🎲 INTERPRÉTATION :
• Mesure l'information théorique contenue dans la séquence
• Basée sur les probabilités INDEX5 connues a priori
• Plus stable que l'entropie empirique
• Unité : bits

═══════════════════════════════════════════════════════════════════════════════
5. DIVENTROPG (Diversité Entropique - Entropie de Shannon Observée)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
La diversité entropique calcule l'entropie de Shannon basée sur les fréquences
observées dans la séquence (entropie empirique).

📐 FORMULE THÉORIQUE :
H(X) = -∑ p_obs(x) log₂ p_obs(x)
où p_obs(x) = fréquence observée de x / longueur totale

🔧 ALGORITHME DÉTAILLÉ :
```python
def calculate_simple_entropy_observed(sequence):
    # Étape 1: Compter les fréquences observées
    counts = Counter(sequence)
    total = len(sequence)
    empirical_probs = [counts[value] / total for value in counts.keys()]

    # Étape 2: Calculer l'entropie de Shannon
    return _calculate_shannon_entropy(empirical_probs)

def _calculate_shannon_entropy(probabilities):
    p = _validate_probabilities(probabilities)  # Normalisation

    # Calcul avec gestion de 0*log(0) = 0
    log_p = _safe_log(p)  # log₂(p) avec gestion de log(0)
    entropy_terms = p * log_p

    # Remplacer NaN par 0 (cas 0*log(0))
    entropy_terms = np.where(p == 0, 0, entropy_terms)

    return -np.sum(entropy_terms)

def _safe_log(x):
    x = np.array(x)
    x = np.where(x <= 0, epsilon, x)  # Remplacer ≤0 par epsilon = 1e-12
    return np.log(x) / np.log(base)  # Logarithme en base spécifiée
```

🎲 INTERPRÉTATION :
• Mesure la diversité réelle observée dans les données
• Sensible aux fréquences empiriques
• Différente de l'entropie théorique (EntropG)
• Unité : bits

═══════════════════════════════════════════════════════════════════════════════
6. DIFFCOND / DIFFC (Différentiel Entropie Conditionnelle)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
Le différentiel d'entropie conditionnelle mesure la variation absolue de l'entropie
conditionnelle entre deux mains consécutives.

📐 FORMULE :
DiffCond(n) = |Conditionnelle(n) - Conditionnelle(n-1)|
DiffC = DiffCond (abréviation dans les tableaux)

🔧 ALGORITHME DÉTAILLÉ :
```python
def calculate_differentials(entropy_evolution):
    differentials = []

    # Première main : différentiel = 0 (pas de main précédente)
    differentials.append({
        'position': 1,
        'diff_conditional': 0.0,
        # ... autres différentiels
    })

    # Calculer les différentiels pour les mains suivantes
    for i in range(1, len(entropy_evolution)):
        current = entropy_evolution[i]
        previous = entropy_evolution[i-1]

        diff_conditional = abs(current.get('conditional_entropy', 0) -
                             previous.get('conditional_entropy', 0))

        differentials.append({
            'position': current.get('position', i+1),
            'diff_conditional': diff_conditional,  # DiffC
            # ... autres différentiels
        })

    return differentials
```

🎲 INTERPRÉTATION :
• DiffC élevé → Changement important dans la prédictibilité contextuelle
• DiffC faible → Stabilité de la prédictibilité
• DiffC = 0 → Aucun changement dans l'entropie conditionnelle
• Unité : bits

═══════════════════════════════════════════════════════════════════════════════
7. DIFFTAUX / DIFFT (Différentiel Taux d'Entropie)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
Le différentiel du taux d'entropie mesure la variation absolue du taux d'entropie
entre deux mains consécutives.

📐 FORMULE :
DiffTaux(n) = |Taux(n) - Taux(n-1)|
DiffT = DiffTaux (abréviation dans les tableaux)

🔧 ALGORITHME DÉTAILLÉ :
```python
# Dans calculate_differentials()
diff_entropy_rate = abs(current.get('entropy_rate', 0) -
                       previous.get('entropy_rate', 0))

# Où entropy_rate est calculé comme :
# Dans calculate_block_entropy_evolution()
'entropy_rate': block_entropies[-1] if block_entropies else 0
```

💻 CALCUL PRÉDICTIF :
```python
# Pour les calculs prédictifs, le taux est calculé sur bloc local de longueur 4
if len(sequence) >= 4:
    local_block_current = sequence[position-3:position+1]  # [n-3,n-2,n-1,n]
    current_rate = analyzer._calculate_sequence_entropy_aep(local_block_current)
else:
    current_rate = current_metrics.get('entropy_rate', 0.0)
```

🎲 INTERPRÉTATION :
• DiffT élevé → Changement important dans le taux de génération d'information
• DiffT faible → Stabilité du taux d'entropie
• DiffT = 0 → Aucun changement dans le taux d'entropie
• Unité : bits

═══════════════════════════════════════════════════════════════════════════════
8. DIFFDIVRENTROPG / DIVEG (Différentiel Diversité Entropique)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
Le différentiel de diversité entropique mesure la variation absolue de l'entropie
de Shannon observée entre deux mains consécutives.

📐 FORMULE :
DiffDivEntropG(n) = |DivEntropG(n) - DivEntropG(n-1)|
                  = |simple_entropy(n) - simple_entropy(n-1)|
DivEG = DiffDivEntropG (abréviation dans les tableaux)

🔧 ALGORITHME DÉTAILLÉ :
```python
# Dans calculate_differentials()
diff_simple_entropy = abs(current.get('simple_entropy', 0) -
                         previous.get('simple_entropy', 0))

# Où simple_entropy est calculé comme :
# Dans calculate_block_entropy_evolution()
counts = Counter(subsequence)
total = len(subsequence)
empirical_probs = [counts[value] / total for value in counts.keys()]
simple_entropy_observed = _calculate_shannon_entropy(empirical_probs)
```

💻 CALCUL PRÉDICTIF :
```python
# Pour les calculs prédictifs
counts = Counter(simulated_sequence)
total = len(simulated_sequence)
probabilities = [counts[value] / total for value in counts.keys()]
simulated_simple = analyzer._calculate_shannon_entropy(probabilities)

# Différentiel
diff_div_entrop = abs(simulated_simple - current_simple)
```

🎲 INTERPRÉTATION :
• DivEG élevé → Changement important dans la diversité observée
• DivEG faible → Stabilité de la diversité entropique
• DivEG = 0 → Aucun changement dans l'entropie observée
• Unité : bits

═══════════════════════════════════════════════════════════════════════════════
9. DIFFENTROPG / ENTG (Différentiel Entropie Générale)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
Le différentiel d'entropie générale mesure la variation absolue de l'entropie
théorique AEP entre deux mains consécutives.

📐 FORMULE :
DiffEntropG(n) = |EntropG(n) - EntropG(n-1)|
               = |simple_entropy_theoretical(n) - simple_entropy_theoretical(n-1)|
EntG = DiffEntropG (abréviation dans les tableaux)

🔧 ALGORITHME DÉTAILLÉ :
```python
# Dans calculate_differentials()
diff_simple_entropy_theoretical = abs(current.get('simple_entropy_theoretical', 0) -
                                     previous.get('simple_entropy_theoretical', 0))

# Où simple_entropy_theoretical est calculé comme :
# Dans calculate_block_entropy_evolution()
simple_entropy_theoretical = _calculate_sequence_entropy_aep(subsequence)
```

💻 CALCUL PRÉDICTIF :
```python
# Pour les calculs prédictifs
# Entropie théorique (AEP) pour la séquence simulée complète
simulated_theoretical = analyzer._calculate_sequence_entropy_aep(simulated_sequence)

# Différentiel
diff_entrop = abs(simulated_theoretical - current_theoretical)
```

🎲 INTERPRÉTATION :
• EntG élevé → Changement important dans l'entropie théorique
• EntG faible → Stabilité de l'entropie théorique AEP
• EntG = 0 → Aucun changement dans l'entropie théorique
• Unité : bits

═══════════════════════════════════════════════════════════════════════════════
10. SCORE PRÉDICTIF COMPOSITE
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
Le score prédictif composite combine les 4 différentiels selon une formule de pondération
pour évaluer l'impact global d'une valeur INDEX5 sur les métriques entropiques.

📐 FORMULE COMPOSITE :
SCORE = (DiffC + EntG) / (DiffT + DivEG)
      = (DiffCond + DiffEntropG) / (DiffTaux + DiffDivEntropG)

🔧 ALGORITHME DE CALCUL :
```python
def calculate_predictive_score(diff_c, diff_t, div_eg, ent_g):
    """
    Calcule le score prédictif selon la formule:
    SCORE = (DiffC + EntG) / (DiffT + DivEG)

    Args:
        diff_c: Différentiel Entropie Conditionnelle (DiffCond)
        diff_t: Différentiel Taux d'Entropie (DiffTaux)
        div_eg: Différentiel Diversité Entropique (DiffDivEntropG)
        ent_g: Différentiel Entropie Générale (DiffEntropG)

    Returns:
        float: Score prédictif (peut être infini si dénominateur = 0)
    """
    # Calcul du dénominateur
    denominator = diff_t + div_eg  # DiffTaux + DiffDivEntropG

    # Gestion du cas dénominateur = 0
    if denominator == 0:
        return float('inf')  # Score infini
    else:
        # Calcul du score normal
        numerator = diff_c + ent_g  # DiffCond + DiffEntropG
        return numerator / denominator
```

🎲 INTERPRÉTATION DU SCORE :
• Score élevé → Forte variation des métriques conditionnelles et théoriques
                par rapport aux métriques de taux et diversité
• Score faible → Faible impact relatif sur les métriques principales
• Score = ∞ → Dénominateur nul (DiffTaux + DiffDivEntropG = 0)
• Score = 0 → Numérateur nul (DiffCond + DiffEntropG = 0)

💻 CALCUL PRÉDICTIF DES DIFFÉRENTIELS :
```python
def calculate_predictive_differentials(sequence, evolution, position, analyzer):
    # Étape 1: Obtenir les métriques actuelles à la main n
    current_metrics = evolution[position]
    current_conditional = current_metrics.get('conditional_entropy', 0.0)
    current_rate = current_metrics.get('entropy_rate', 0.0)
    current_simple = current_metrics.get('simple_entropy', 0.0)
    current_theoretical = current_metrics.get('simple_entropy_theoretical', 0.0)

    predictive_differentials = {}

    # Étape 2: Pour chaque valeur INDEX5 possible selon règles INDEX1
    for possible_index5 in get_possible_index5_values(sequence[position-1]):
        # Étape 3: Simuler l'ajout de cette valeur
        simulated_sequence = sequence[:position+1] + [possible_index5]

        # Étape 4: Calculer les nouvelles métriques simulées
        simulated_conditional = analyzer._calculate_conditional_entropy(simulated_sequence)
        simulated_simple = calculate_shannon_entropy_from_sequence(simulated_sequence)
        simulated_theoretical = analyzer._calculate_sequence_entropy_aep(simulated_sequence)
        simulated_rate = calculate_entropy_rate(simulated_sequence)

        # Étape 5: Calculer les différentiels absolus
        diff_cond = abs(simulated_conditional - current_conditional)      # DiffC
        diff_taux = abs(simulated_rate - current_rate)                   # DiffT
        diff_div_entrop = abs(simulated_simple - current_simple)         # DivEG
        diff_entrop = abs(simulated_theoretical - current_theoretical)   # EntG

        # Étape 6: Calculer le score composite
        score = calculate_predictive_score(diff_cond, diff_taux, diff_div_entrop, diff_entrop)

        predictive_differentials[possible_index5] = {
            'DiffCond': diff_cond,        # DiffC
            'DiffTaux': diff_taux,        # DiffT
            'DiffDivEntropG': diff_div_entrop,  # DivEG
            'DiffEntropG': diff_entrop,   # EntG
            'SCORE': score
        }

    return predictive_differentials
```

═══════════════════════════════════════════════════════════════════════════════
11. H_AEP (Formule Unifiée - Asymptotic Equipartition Property)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
H_AEP est la formule unifiée utilisée dans tout le programme pour calculer l'entropie
d'une séquence selon la Propriété d'Équipartition Asymptotique.

📐 FORMULE THÉORIQUE AEP :
H_AEP(séquence) = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
Pour séquences indépendantes : p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
Donc : H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))

🔧 ALGORITHME COMPLET :
```python
def _calculate_sequence_entropy_aep(sequence):
    if not sequence:
        return 0.0

    # Calculer log₂ de la probabilité jointe théorique
    total_log_prob = 0.0
    for value in sequence:
        if value in theoretical_probs:
            p_theo = theoretical_probs[value]
            if p_theo > 0:
                total_log_prob += math.log2(p_theo)
            else:
                total_log_prob += math.log2(epsilon)  # epsilon = 1e-12
        else:
            total_log_prob += math.log2(epsilon)  # Valeur non trouvée

    # Retourner l'entropie AEP : -(1/n) × ∑log₂(p_théo(xᵢ))
    return -total_log_prob / len(sequence)
```

🎲 UTILISATIONS DANS LE PROGRAMME :
• EntropG (Entropie Générale)
• Calcul des entropies de blocs
• Entropie conditionnelle (pour chaque contexte)
• Calculs prédictifs (entropie théorique simulée)
• Taux d'entropie sur blocs locaux

📊 EXEMPLE DE CALCUL :
Séquence : ["1_A_BANKER", "0_C_TIE", "1_B_PLAYER"]
• p("1_A_BANKER") = 0.086389 → log₂(0.086389) = -3.5336
• p("0_C_TIE") = 0.013241 → log₂(0.013241) = -6.2376
• p("1_B_PLAYER") = 0.077888 → log₂(0.077888) = -3.6835
• total_log_prob = -3.5336 + (-6.2376) + (-3.6835) = -13.4547
• H_AEP = -(-13.4547) / 3 = 4.4849 bits

═══════════════════════════════════════════════════════════════════════════════
12. RÈGLES INDEX1 (Transitions Déterministes)
═══════════════════════════════════════════════════════════════════════════════

🎯 DÉFINITION :
Les règles INDEX1 sont des contraintes déterministes qui déterminent la valeur
obligatoire d'INDEX1 à la main n+1 en fonction de la valeur INDEX2 à la main n.

📐 FORMULE MATHÉMATIQUE UNIFIÉE :
```
INDEX1(n+1) = {
    1 - INDEX1(n)  si INDEX2(n) = 'C'  (inversion)
    INDEX1(n)      si INDEX2(n) ∈ {'A', 'B'}  (conservation)
}
```

🔧 IMPLÉMENTATION INFORMATIQUE :
```python
def calculate_required_index1(current_index5):
    if not current_index5:
        return None

    try:
        parts = current_index5.split('_')
        current_index1 = int(parts[0])  # 0 ou 1
        current_index2 = parts[1]       # A, B ou C

        if current_index2 == 'C':
            return 1 - current_index1   # Inversion obligatoire
        else:  # A ou B
            return current_index1       # Conservation obligatoire
    except:
        return None
```

📊 RÈGLES DÉTAILLÉES :
• Si INDEX1=0, INDEX2=C → INDEX1(n+1) = 1 - 0 = 1
• Si INDEX1=1, INDEX2=C → INDEX1(n+1) = 1 - 1 = 0
• Si INDEX1=0, INDEX2=A → INDEX1(n+1) = 0
• Si INDEX1=1, INDEX2=A → INDEX1(n+1) = 1
• Si INDEX1=0, INDEX2=B → INDEX1(n+1) = 0
• Si INDEX1=1, INDEX2=B → INDEX1(n+1) = 1

🎲 IMPACT :
• Réduit les 18 valeurs INDEX5 possibles à 9 valeurs valides
• Garantit la cohérence déterministe des transitions
• Appliqué dans tous les calculs prédictifs

═══════════════════════════════════════════════════════════════════════════════
CALCUL DU SCORE PRÉDICTIF COMPOSITE
═══════════════════════════════════════════════════════════════════════════════

FORMULE COMPOSITE :
SCORE = (DiffC + EntG) / (DiffT + DivEG)

DÉVELOPPEMENT COMPLET :
SCORE = (DiffCond + DiffEntropG) / (DiffTaux + DiffDivEntropG)

ALGORITHME DE CALCUL :
```python
def calculate_predictive_score(diff_c, diff_t, div_eg, ent_g):
    """
    Calcule le score prédictif selon la formule:
    SCORE = (DiffC + EntG) / (DiffT + DivEG)

    Args:
        diff_c: Différentiel Entropie Conditionnelle (DiffCond)
        diff_t: Différentiel Taux d'Entropie (DiffTaux)
        div_eg: Différentiel Diversité Entropique (DiffDivEntropG)
        ent_g: Différentiel Entropie Générale (DiffEntropG)

    Returns:
        float: Score prédictif (peut être infini si dénominateur = 0)
    """
    # Calcul du dénominateur
    denominator = diff_t + div_eg  # DiffTaux + DiffDivEntropG

    # Gestion du cas dénominateur = 0
    if denominator == 0:
        return float('inf')  # Score infini
    else:
        # Calcul du score normal
        numerator = diff_c + ent_g  # DiffCond + DiffEntropG
        return numerator / denominator
```

INTERPRÉTATION DU SCORE :
• Score élevé → Forte variation des métriques conditionnelles et théoriques
                par rapport aux métriques de taux et diversité
• Score faible → Faible impact relatif sur les métriques principales
• Score = ∞ → Dénominateur nul (DiffTaux + DiffDivEntropG = 0)
• Score = 0 → Numérateur nul (DiffCond + DiffEntropG = 0)

UTILISATION DANS LES TABLEAUX PRÉDICTIFS :
```python
# Pour chaque valeur INDEX5 possible
for possible_index5 in get_possible_index5_values():
    # Calculer les différentiels prédictifs
    diffs = calculate_predictive_differentials(sequence, position, possible_index5)

    # Calculer le score composite
    score = calculate_predictive_score(
        diffs['DiffCond'],      # DiffC
        diffs['DiffTaux'],      # DiffT
        diffs['DiffDivEntropG'], # DivEG
        diffs['DiffEntropG']    # EntG
    )

    # Afficher dans le tableau
    print(f"{possible_index5}: {score:.4f}")
```

VÉRIFICATION MANUELLE DU CALCUL :
```python
# Calcul manuel pour vérification
manual_score = (diffs['DiffCond'] + diffs['DiffEntropG']) / (diffs['DiffTaux'] + diffs['DiffDivEntropG'])

# Doit être identique au score calculé par la méthode
assert abs(calculated_score - manual_score) < 1e-10
```

═══════════════════════════════════════════════════════════════════════════════
FORMAT DES TABLEAUX PRÉDICTIFS
═══════════════════════════════════════════════════════════════════════════════

TABLEAU AVEC DIFFÉRENTIELS DÉTAILLÉS :
```
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | DiffCond | DiffTaux | DiffDivEntropG | DiffEntropG
---------|-------------|----------|----------------|------|-------------|--------------|----------|----------|----------------|------------
Main  6  | 0_A_BANKER  |   2.456  |        3.789   | 2.34 |     1.567   |      2.890   |   0.123  |   0.045  |      0.234     |    0.156
```

TABLEAU PRÉDICTIF AVEC DIFFÉRENTIELS :
```
|Main 6                  |Main 7                  |Main 8                  |
|DiffC|DiffT|DivEG|EntG  |DiffC|DiffT|DivEG|EntG  |DiffC|DiffT|DivEG|EntG  |
|-----|-----|-----|------|-----|-----|-----|------|-----|-----|-----|------|
|0.123|0.045|0.234|0.156 |0.234|0.067|0.345|0.267 |0.345|0.089|0.456|0.378 |
```

TABLEAU PRÉDICTIF AVEC SCORES :
```
|Main 6    |Main 7    |Main 8    |
|SCORE     |SCORE     |SCORE     |
|----------|----------|----------|
|  1.2345  |  2.3456  |  3.4567  |
```

═══════════════════════════════════════════════════════════════════════════════
GÉNÉRATION DES DIFFÉRENTIELS PRÉDICTIFS
═══════════════════════════════════════════════════════════════════════════════

PROCESSUS COMPLET POUR CALCULER DiffC, DiffT, DivEG, EntG :

ÉTAPE 1 - OBTENIR LES MÉTRIQUES ACTUELLES :
```python
# À la main n, récupérer les métriques actuelles
current_metrics = evolution[position]
current_conditional = current_metrics.get('conditional_entropy', 0.0)
current_rate = current_metrics.get('entropy_rate', 0.0)
current_simple = current_metrics.get('simple_entropy', 0.0)
current_theoretical = current_metrics.get('simple_entropy_theoretical', 0.0)
```

ÉTAPE 2 - SIMULER CHAQUE VALEUR INDEX5 POSSIBLE :
```python
# Pour chaque valeur INDEX5 possible selon règles INDEX1
for possible_index5 in get_possible_index5_values(reference_index5):
    # Créer séquence simulée avec la valeur ajoutée
    simulated_sequence = sequence[:position+1] + [possible_index5]
```

ÉTAPE 3 - CALCULER LES NOUVELLES MÉTRIQUES SIMULÉES :
```python
    # Entropie conditionnelle simulée
    simulated_conditional = analyzer._calculate_conditional_entropy(simulated_sequence)

    # Entropie simple simulée (Shannon sur fréquences observées)
    counts = Counter(simulated_sequence)
    total = len(simulated_sequence)
    empirical_probs = [counts[value] / total for value in counts.keys()]
    simulated_simple = analyzer._calculate_shannon_entropy(empirical_probs)

    # Entropie théorique simulée (AEP)
    simulated_theoretical = analyzer._calculate_sequence_entropy_aep(simulated_sequence)

    # Taux d'entropie simulé (sur bloc local de longueur 4)
    if len(simulated_sequence) >= 4:
        local_block_simulated = simulated_sequence[-4:]  # 4 derniers éléments
        simulated_rate = analyzer._calculate_sequence_entropy_aep(local_block_simulated)
    else:
        simulated_rate = simulated_simple
```

ÉTAPE 4 - CALCULER LES DIFFÉRENTIELS ABSOLUS :
```python
    # Calculer les différentiels absolus |métrique(n+1) - métrique(n)|
    diff_cond = abs(simulated_conditional - current_conditional)      # DiffC
    diff_taux = abs(simulated_rate - current_rate)                   # DiffT
    diff_div_entrop = abs(simulated_simple - current_simple)         # DivEG
    diff_entrop = abs(simulated_theoretical - current_theoretical)   # EntG

    # Stocker les résultats
    predictive_differentials[possible_index5] = {
        'DiffCond': diff_cond,        # DiffC
        'DiffTaux': diff_taux,        # DiffT
        'DiffDivEntropG': diff_div_entrop,  # DivEG
        'DiffEntropG': diff_entrop    # EntG
    }
```

ÉTAPE 5 - CALCULER LE SCORE COMPOSITE :
```python
    # Calculer le score prédictif pour cette valeur
    score = calculate_predictive_score(
        diff_cond,        # DiffC
        diff_taux,        # DiffT
        diff_div_entrop,  # DivEG
        diff_entrop       # EntG
    )
```


VALEURS INDEX5 POSSIBLES (9 au total) :
```python
possible_index5_values = [
    "0_A_BANKER", "1_A_BANKER",
    "0_B_BANKER", "1_B_BANKER",
    "0_C_BANKER", "1_C_BANKER",
    "0_A_PLAYER", "1_A_PLAYER",
    "0_B_PLAYER", "1_B_PLAYER",
    "0_C_PLAYER", "1_C_PLAYER",
    "0_A_TIE", "1_A_TIE",
    "0_B_TIE", "1_B_TIE",
    "0_C_TIE", "1_C_TIE"
]
```

Seules ces 9 valeurs sont calculées dans les tableaux prédictifs.
Les autres combinaisons donnent "N/A" (ne respectent pas les règles INDEX1).

═══════════════════════════════════════════════════════════════════════════════
EXEMPLE COMPLET DE CALCUL
═══════════════════════════════════════════════════════════════════════════════

SITUATION :
• Main actuelle n = 6
• INDEX5(5) = "0_A_BANKER" (référence pour règles INDEX1)
• Séquence actuelle : ["1_B_PLAYER", "0_C_TIE", "1_A_BANKER", "0_B_PLAYER", "1_C_BANKER", "0_A_BANKER"]

MÉTRIQUES ACTUELLES À LA MAIN 6 :
• Conditionnelle = 3.789
• Taux = 2.340
• Simple = 1.567
• Théorique = 2.890

CALCUL POUR possible_index5 = "1_A_PLAYER" :

1. Séquence simulée = ["1_B_PLAYER", "0_C_TIE", "1_A_BANKER", "0_B_PLAYER", "1_C_BANKER", "0_A_BANKER", "1_A_PLAYER"]

2. Métriques simulées :
   • simulated_conditional = 3.912
   • simulated_rate = 2.385
   • simulated_simple = 1.801
   • simulated_theoretical = 3.046

3. Différentiels :
   • DiffC = |3.912 - 3.789| = 0.123
   • DiffT = |2.385 - 2.340| = 0.045
   • DivEG = |1.801 - 1.567| = 0.234
   • EntG = |3.046 - 2.890| = 0.156

4. Score :
   • SCORE = (0.123 + 0.156) / (0.045 + 0.234) = 0.279 / 0.279 = 1.000

RÉSULTAT FINAL :
```
1_A_PLAYER: DiffC=0.123, DiffT=0.045, DivEG=0.234, EntG=0.156, SCORE=1.000
```

═══════════════════════════════════════════════════════════════════════════════
H_AEP - ENTROPIE SELON LA PROPRIÉTÉ D'ÉQUIPARTITION ASYMPTOTIQUE
═══════════════════════════════════════════════════════════════════════════════

DÉFINITION :
H_AEP est l'entropie d'une séquence calculée selon la Propriété d'Équipartition
Asymptotique (Asymptotic Equipartition Property). Cette formule unifiée est utilisée
dans tout le programme pour garantir la cohérence mathématique.

FORMULE THÉORIQUE AEP :
H_AEP(séquence) = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)

Pour des séquences indépendantes :
p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)

Donc :
log₂(p(séquence)) = ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))

FORMULE FINALE :
H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))

RÉFÉRENCE THÉORIQUE :
"Elements of Information Theory" - ligne 1919
"The AEP states that (1/n) log (1/p(X₁, X₂, ..., Xₙ)) is close to the entropy H"

ALGORITHME COMPLET :
```python
def _calculate_sequence_entropy_aep(sequence):
    """
    Calcule l'entropie d'une séquence selon la formule AEP exacte :
    H_séquence = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
    où p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ) pour séquences indépendantes

    Args:
        sequence: Liste des valeurs INDEX5 dans la séquence

    Returns:
        float: Entropie de la séquence selon AEP en bits
    """
    if not sequence:
        return 0.0

    # Calculer -log₂ de la probabilité jointe théorique
    # p(séquence) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
    # log₂(p(séquence)) = ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
    total_log_prob = 0.0
    for value in sequence:
        if value in theoretical_probs:
            p_theo = theoretical_probs[value]
            if p_theo > 0:
                total_log_prob += math.log2(p_theo)
            else:
                # Si probabilité théorique = 0, utiliser epsilon
                total_log_prob += math.log2(epsilon)
        else:
            # Si valeur non trouvée, utiliser epsilon
            total_log_prob += math.log2(epsilon)

    # Retourner l'entropie AEP : -(1/n) × ∑log₂(p_théo(xᵢ))
    return -total_log_prob / len(sequence)
```

PROBABILITÉS THÉORIQUES INDEX5 UTILISÉES :
```python
theoretical_probs = {
    '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389,
    '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479,
    '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929,
    '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361,
    '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888,
    '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352,
    '0_A_TIE': 0.017719, '1_A_TIE': 0.017978,
    '0_B_TIE': 0.016281, '1_B_TIE': 0.016482,
    '0_C_TIE': 0.013241, '1_C_TIE': 0.013423
}
# Normalisées pour que la somme = 1.0
```

GESTION DES CAS LIMITES :
• Si séquence vide → retourner 0.0
• Si probabilité théorique = 0 → utiliser epsilon (1e-12)
• Si valeur INDEX5 non trouvée → utiliser epsilon
• epsilon = 1e-12 (valeur très faible pour éviter log(0))

UTILISATIONS DE H_AEP DANS LE PROGRAMME :

1. **ENTROPG (Entropie Générale)** :
   ```python
   simple_entropy_theoretical = _calculate_sequence_entropy_aep(subsequence)
   ```

2. **Calcul des entropies de blocs** :
   ```python
   # Pour chaque bloc de séquence
   block_entropy = _calculate_sequence_entropy_aep(block_sequence)
   ```

3. **Entropie conditionnelle** :
   ```python
   # Pour chaque contexte
   context_entropy = _calculate_sequence_entropy_aep(context_sequence)
   ```

4. **Calculs prédictifs** :
   ```python
   # Entropie théorique simulée
   simulated_theoretical = _calculate_sequence_entropy_aep(simulated_sequence)

   # Taux d'entropie sur bloc local
   simulated_rate = _calculate_sequence_entropy_aep(local_block_simulated)
   ```

EXEMPLE DE CALCUL H_AEP :

SÉQUENCE : ["1_A_BANKER", "0_C_TIE", "1_B_PLAYER"]

ÉTAPE 1 - Récupérer les probabilités théoriques :
• p("1_A_BANKER") = 0.086389
• p("0_C_TIE") = 0.013241
• p("1_B_PLAYER") = 0.077888

ÉTAPE 2 - Calculer les logarithmes :
• log₂(0.086389) = -3.5336
• log₂(0.013241) = -6.2376
• log₂(0.077888) = -3.6835

ÉTAPE 3 - Somme des logarithmes :
total_log_prob = -3.5336 + (-6.2376) + (-3.6835) = -13.4547

ÉTAPE 4 - Calcul final H_AEP :
H_AEP = -(-13.4547) / 3 = 13.4547 / 3 = 4.4849 bits

RÉSULTAT : H_AEP = 4.4849 bits

AVANTAGES DE LA FORMULE AEP :
• **Cohérence mathématique** : Formule unifiée pour tous les calculs
• **Théoriquement fondée** : Basée sur la théorie de l'information
• **Probabilités exactes** : Utilise les vraies probabilités INDEX5
• **Gestion robuste** : Traite les cas limites (log(0), valeurs manquantes)
• **Performance** : Calcul direct sans itérations complexes

DIFFÉRENCE AVEC L'ENTROPIE DE SHANNON CLASSIQUE :
• **Shannon classique** : H = -∑ p_obs(x) log₂ p_obs(x) (fréquences observées)
• **AEP** : H_AEP = -(1/n) × ∑ log₂ p_théo(xᵢ) (probabilités théoriques)

L'AEP utilise les probabilités théoriques connues au lieu des fréquences observées,
ce qui donne une mesure plus stable et théoriquement correcte de l'information
contenue dans la séquence.

═══════════════════════════════════════════════════════════════════════════════
RÈGLES DE TRANSITION INDEX1 DÉTERMINISTES
═══════════════════════════════════════════════════════════════════════════════

DÉFINITION :
Les règles INDEX1 sont des contraintes déterministes qui déterminent la valeur
obligatoire d'INDEX1 à la main n+1 en fonction de la valeur INDEX2 à la main n.
Ces règles réduisent les 18 valeurs INDEX5 possibles à seulement 9 valeurs valides.

FORMAT INDEX5 :
INDEX5 = "INDEX1_INDEX2_INDEX3"
• INDEX1 ∈ {0, 1} (bit déterministe)
• INDEX2 ∈ {A, B, C} (position dans la séquence)
• INDEX3 ∈ {BANKER, PLAYER, TIE} (résultat du coup)

RÈGLES DÉTERMINISTES EXACTES :

RÈGLE 1 - Si INDEX2(n) = 'C' : INDEX1 s'INVERSE
• Si INDEX1(n) = 0 → INDEX1(n+1) = 1 (inversion obligatoire)
• Si INDEX1(n) = 1 → INDEX1(n+1) = 0 (inversion obligatoire)

RÈGLE 2 - Si INDEX2(n) = 'A' ou 'B' : INDEX1 se CONSERVE
• Si INDEX1(n) = 0 → INDEX1(n+1) = 0 (conservation obligatoire)
• Si INDEX1(n) = 1 → INDEX1(n+1) = 1 (conservation obligatoire)

ALGORITHME DE CALCUL :
```python
def calculate_required_index1(current_index5):
    """
    Calcule INDEX1 obligatoire selon les règles déterministes

    Règles:
    - Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
    - Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)

    Args:
        current_index5: Valeur INDEX5 à la main n (ex: "0_A_BANKER")

    Returns:
        int: INDEX1 obligatoire pour la main n+1 (0 ou 1)
        None: Si erreur de parsing
    """
    if not current_index5:
        return None

    try:
        # Parser INDEX5 : "INDEX1_INDEX2_INDEX3"
        parts = current_index5.split('_')
        current_index1 = int(parts[0])  # 0 ou 1
        current_index2 = parts[1]       # A, B ou C

        # Appliquer les règles déterministes
        if current_index2 == 'C':
            return 1 - current_index1   # Inversion obligatoire
        else:  # A ou B
            return current_index1       # Conservation obligatoire

    except (IndexError, ValueError):
        return None  # Erreur de parsing
```

GÉNÉRATION DES VALEURS VALIDES :
```python
def get_valid_index5_values(required_index1):
    """
    Retourne les 9 valeurs INDEX5 possibles avec INDEX1 obligatoire

    Args:
        required_index1: INDEX1 obligatoire (0 ou 1)

    Returns:
        list: Liste des 9 valeurs INDEX5 valides
    """
    if required_index1 is None:
        return []

    valid_values = []
    for index2 in ['A', 'B', 'C']:
        for index3 in ['BANKER', 'PLAYER', 'TIE']:
            valid_values.append(f"{required_index1}_{index2}_{index3}")

    return valid_values
```

EXEMPLES CONCRETS :

EXEMPLE 1 - Conservation (INDEX2 = A) :
• Main n : "0_A_BANKER" → INDEX1=0, INDEX2=A
• Règle : INDEX2=A → Conservation
• INDEX1(n+1) = INDEX1(n) = 0
• Valeurs possibles main n+1 : ["0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
                                "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER",
                                "0_A_TIE", "0_B_TIE", "0_C_TIE"]

EXEMPLE 2 - Conservation (INDEX2 = B) :
• Main n : "1_B_PLAYER" → INDEX1=1, INDEX2=B
• Règle : INDEX2=B → Conservation
• INDEX1(n+1) = INDEX1(n) = 1
• Valeurs possibles main n+1 : ["1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
                                "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER",
                                "1_A_TIE", "1_B_TIE", "1_C_TIE"]

EXEMPLE 3 - Inversion (INDEX2 = C) :
• Main n : "0_C_TIE" → INDEX1=0, INDEX2=C
• Règle : INDEX2=C → Inversion
• INDEX1(n+1) = 1 - INDEX1(n) = 1 - 0 = 1
• Valeurs possibles main n+1 : ["1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
                                "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER",
                                "1_A_TIE", "1_B_TIE", "1_C_TIE"]

EXEMPLE 4 - Inversion (INDEX2 = C) :
• Main n : "1_C_BANKER" → INDEX1=1, INDEX2=C
• Règle : INDEX2=C → Inversion
• INDEX1(n+1) = 1 - INDEX1(n) = 1 - 1 = 0
• Valeurs possibles main n+1 : ["0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
                                "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER",
                                "0_A_TIE", "0_B_TIE", "0_C_TIE"]

TABLEAU RÉCAPITULATIF DES TRANSITIONS :

| Main n (INDEX2) | INDEX1(n) | Règle        | INDEX1(n+1) | Nb valeurs possibles |
|------------------|-----------|--------------|--------------|---------------------|
| *_A_*           | 0         | Conservation | 0            | 9                   |
| *_A_*           | 1         | Conservation | 1            | 9                   |
| *_B_*           | 0         | Conservation | 0            | 9                   |
| *_B_*           | 1         | Conservation | 1            | 9                   |
| *_C_*           | 0         | Inversion    | 1            | 9                   |
| *_C_*           | 1         | Inversion    | 0            | 9                   |

APPLICATION DANS LES CALCULS PRÉDICTIFS :

1. **Filtrage des prédictions** :
   ```python
   # Seules les prédictions respectant INDEX1 sont conservées
   pred_filtered = filter_prediction_by_constraint(pred_raw, valid_index5_values)
   ```

2. **Génération des tableaux prédictifs** :
   ```python
   # Calculer différentiels seulement pour les 9 valeurs valides
   for possible_index5 in valid_index5_values:
       differentials[possible_index5] = calculate_differentials(...)
   ```

3. **Validation des contraintes** :
   ```python
   # Vérifier qu'une prédiction respecte les règles INDEX1
   if prediction in valid_index5_values:
       return prediction  # Valide
   else:
       return None        # Invalide
   ```

IMPACT SUR LES CALCULS :
• **Réduction de complexité** : 18 → 9 valeurs à calculer
• **Cohérence déterministe** : Respect des contraintes obligatoires
• **Performance** : Moins de calculs prédictifs nécessaires
• **Précision** : Élimination des valeurs impossibles

GESTION DES ERREURS :
• Si INDEX5 mal formaté → retourner None
• Si INDEX1 non numérique → retourner None
• Si INDEX2 non reconnu → traiter comme A/B (conservation)
• Si parsing échoue → retourner liste vide

UTILISATION DANS LE PROGRAMME :
Ces règles sont appliquées systématiquement dans :
• INDEX5Predictor.predict_next_index5()
• INDEX5PredictiveDifferentialTable.calculate_predictive_differentials()
• INDEX5ScoreTable.calculate_predictive_scores()
• Tous les calculs de tableaux prédictifs

Les règles INDEX1 garantissent que seules les transitions déterministiquement
possibles sont considérées dans les analyses et prédictions entropiques.

═══════════════════════════════════════════════════════════════════════════════
IMPLÉMENTATION INFORMATIQUE DES RÈGLES INDEX1
═══════════════════════════════════════════════════════════════════════════════

CONVERSION DES RÈGLES EN CODE :
Les règles déterministes INDEX1 sont implémentées dans le programme par plusieurs
méthodes identiques qui utilisent la même logique mathématique.

RÈGLES ORIGINALES À IMPLÉMENTER :
• Si INDEX1 = 0 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 1
• Si INDEX1 = 1 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 0
• Si INDEX1 = 0 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 0
• Si INDEX1 = 1 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 1
• Si INDEX1 = 0 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 0
• Si INDEX1 = 1 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 1

FORMULE MATHÉMATIQUE UNIFIÉE :
```
INDEX1(n+1) = {
    1 - INDEX1(n)  si INDEX2(n) = 'C'  (inversion)
    INDEX1(n)      si INDEX2(n) ∈ {'A', 'B'}  (conservation)
}
```

MÉTHODE 1 - INDEX5Predictor.calculate_required_index1() :
```python
def calculate_required_index1(self, current_index5):
    """
    Calcule INDEX1 obligatoire selon les règles déterministes
    Règles INDEX1:
    - Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
    - Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
    """
    if not current_index5:
        return None

    try:
        current_parts = current_index5.split('_')
        current_index1 = int(current_parts[0])  # Extraire INDEX1 (0 ou 1)
        current_index2 = current_parts[1]       # Extraire INDEX2 (A, B ou C)

        if current_index2 == 'C':
            return 1 - current_index1  # Inversion obligatoire
        else:  # A ou B
            return current_index1      # Conservation obligatoire
    except:
        return None
```

MÉTHODE 2 - INDEX5Predictor.apply_index1_constraint() :
```python
def apply_index1_constraint(self, current_index5, predicted_index5):
    """
    Applique la contrainte INDEX1 déterministe à la prédiction
    Règles INDEX1:
    - Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
    - Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
    """
    if not current_index5 or not predicted_index5:
        return predicted_index5

    try:
        # Extraire INDEX1 et INDEX2 actuels
        current_parts = current_index5.split('_')
        current_index1 = int(current_parts[0])
        current_index2 = current_parts[1]

        # Calculer INDEX1 obligatoire pour n+1 selon les règles déterministes
        if current_index2 == 'C':
            required_index1 = 1 - current_index1  # Inversion obligatoire
        else:  # A ou B
            required_index1 = current_index1      # Conservation obligatoire

        # Extraire INDEX2 et INDEX3 de la prédiction
        predicted_parts = predicted_index5.split('_')
        if len(predicted_parts) >= 3:
            predicted_index2 = predicted_parts[1]
            predicted_index3 = predicted_parts[2]

            # Construire INDEX5 contraint avec INDEX1 déterministe
            constrained_index5 = f"{required_index1}_{predicted_index2}_{predicted_index3}"
            return constrained_index5

    except (IndexError, ValueError):
        # En cas d'erreur, retourner la prédiction originale
        pass

    return predicted_index5
```

MÉTHODE 3 - INDEX5PredictiveDifferentialTable.calculate_required_index1() :
```python
def calculate_required_index1(self, current_index5):
    """
    Calcule INDEX1 obligatoire selon les règles déterministes

    Règles:
    - Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
    - Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
    """
    if not current_index5:
        return None

    try:
        parts = current_index5.split('_')
        current_index1 = int(parts[0])
        current_index2 = parts[1]

        if current_index2 == 'C':
            return 1 - current_index1  # Inversion
        else:  # A ou B
            return current_index1      # Conservation
    except:
        return None
```

ANALYSE MATHÉMATIQUE DE LA FORMULE :

OPÉRATION D'INVERSION (INDEX2 = 'C') :
• Formule : INDEX1(n+1) = 1 - INDEX1(n)
• Si INDEX1(n) = 0 → INDEX1(n+1) = 1 - 0 = 1 ✓
• Si INDEX1(n) = 1 → INDEX1(n+1) = 1 - 1 = 0 ✓
• Propriété : Fonction bijective f(x) = 1 - x sur {0,1}
• Inverse : f⁻¹(x) = 1 - x (fonction involutive)

OPÉRATION DE CONSERVATION (INDEX2 ∈ {'A', 'B'}) :
• Formule : INDEX1(n+1) = INDEX1(n)
• Si INDEX1(n) = 0 → INDEX1(n+1) = 0 ✓
• Si INDEX1(n) = 1 → INDEX1(n+1) = 1 ✓
• Propriété : Fonction identité f(x) = x sur {0,1}
• Inverse : f⁻¹(x) = x (fonction identité)

VÉRIFICATION DES 6 CAS ORIGINAUX :

CAS 1 : INDEX1=0, INDEX2=C → INDEX1(n+1) = 1 - 0 = 1 ✓
CAS 2 : INDEX1=1, INDEX2=C → INDEX1(n+1) = 1 - 1 = 0 ✓
CAS 3 : INDEX1=0, INDEX2=A → INDEX1(n+1) = 0 ✓
CAS 4 : INDEX1=1, INDEX2=A → INDEX1(n+1) = 1 ✓
CAS 5 : INDEX1=0, INDEX2=B → INDEX1(n+1) = 0 ✓
CAS 6 : INDEX1=1, INDEX2=B → INDEX1(n+1) = 1 ✓

PROPRIÉTÉS MATHÉMATIQUES :

1. DÉTERMINISME COMPLET :
   • Chaque état (INDEX1, INDEX2) a exactement un successeur INDEX1
   • Pas d'ambiguïté ni de choix multiple
   • Fonction totale sur le domaine {0,1} × {'A','B','C'}

2. BIJECTIVITÉ CONDITIONNELLE :
   • Pour INDEX2 = 'C' : Bijection {0↔1, 1↔0}
   • Pour INDEX2 ∈ {'A','B'} : Bijection {0↔0, 1↔1}

3. STABILITÉ :
   • Conservation : Points fixes (0→0, 1→1)
   • Inversion : Cycle de longueur 2 (0→1→0, 1→0→1)

4. RÉVERSIBILITÉ :
   • Connaissant INDEX1(n+1) et INDEX2(n), on peut retrouver INDEX1(n)
   • INDEX1(n) = INDEX1(n+1) si INDEX2(n) ∈ {'A','B'}
   • INDEX1(n) = 1 - INDEX1(n+1) si INDEX2(n) = 'C'

OPTIMISATIONS INFORMATIQUES :

1. PARSING SÉCURISÉ :
   • Vérification de la structure "INDEX1_INDEX2_INDEX3"
   • Gestion des erreurs de conversion int()
   • Retour None en cas d'échec

2. PERFORMANCE :
   • Opération O(1) : Une seule condition et une opération arithmétique
   • Pas de boucles ni de structures complexes
   • Cache possible pour éviter les recalculs

3. ROBUSTESSE :
   • Gestion des valeurs nulles ou vides
   • Try-catch pour les erreurs de parsing
   • Comportement défini pour tous les cas d'erreur

UTILISATION DANS LE PROGRAMME :

1. PRÉDICTION (INDEX5Predictor) :
   • Calcul des valeurs INDEX1 possibles
   • Filtrage des prédictions invalides
   • Application des contraintes déterministes

2. TABLEAUX DIFFÉRENTIELS (INDEX5PredictiveDifferentialTable) :
   • Génération des 9 valeurs possibles
   • Calcul des différentiels pour chaque possibilité
   • Exclusion des valeurs non conformes

3. VALIDATION :
   • Vérification de la cohérence des séquences
   • Détection des violations des règles INDEX1
   • Correction automatique des prédictions

═══════════════════════════════════════════════════════════════════════════════
RÉSUMÉ DES FORMULES PRINCIPALES
═══════════════════════════════════════════════════════════════════════════════

📊 MÉTRIQUES DE BASE :
1. MÉTRIQUE : h_μ(T) = H(max_length)/max_length
2. CONDITIONNELLE : H(X|Y) = ∑ P(y) × H(X|y) avec contexte longueur 1
3. TAUX : H_rate = block_entropies[-1]
4. ENTROPG : H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
5. DIVENTROPG : H_Shannon = -∑ p_obs(x) log₂ p_obs(x)

📈 DIFFÉRENTIELS :
6. DIFFCOND/DIFFC : |Conditionnelle(n) - Conditionnelle(n-1)|
7. DIFFTAUX/DIFFT : |Taux(n) - Taux(n-1)|
8. DIFFDIVRENTROPG/DIVEG : |DivEntropG(n) - DivEntropG(n-1)|
9. DIFFENTROPG/ENTG : |EntropG(n) - EntropG(n-1)|

🎯 SCORE COMPOSITE :
10. SCORE = (DiffC + EntG) / (DiffT + DivEG)

🔧 FORMULE UNIFIÉE :
11. H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))

⚡ RÈGLES DÉTERMINISTES :
12. INDEX1(n+1) = {1-INDEX1(n) si INDEX2(n)='C', INDEX1(n) si INDEX2(n)∈{'A','B'}}

═══════════════════════════════════════════════════════════════════════════════
CORRESPONDANCES DES ABRÉVIATIONS
═══════════════════════════════════════════════════════════════════════════════

📋 TABLEAUX PRÉDICTIFS :
• DiffC = DiffCond (Différentiel Entropie Conditionnelle)
• DiffT = DiffTaux (Différentiel Taux d'Entropie)
• DivEG = DiffDivEntropG (Différentiel Diversité Entropique)
• EntG = DiffEntropG (Différentiel Entropie Générale)
• SCORE = (DiffC + EntG) / (DiffT + DivEG)

Cette documentation contient toutes les formules mathématiques nécessaires pour
reproduire exactement tous les calculs du programme entropie_baccarat_analyzer.py.

